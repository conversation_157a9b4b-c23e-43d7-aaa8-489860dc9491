"""JavSP API服务器
提供基于HTTP的API接口，通过番号获取元数据
"""

import os
import sys
import json
import logging
import argparse
from typing import Dict, List, Optional
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

from javsp.datatype import Movie, MovieInfo
from javsp.web.base import download
from javsp.web.exceptions import *
from javsp.web.translate import translate_movie_info
from javsp.config import Cfg, CrawlerID
from javsp.__main__ import (
    import_crawlers,
    error_exit,
    parallel_crawler,
    info_summary,
    generate_names,
    process_poster,
    download_cover,
)

# 设置日志
logger = logging.getLogger("server")
handler = logging.StreamHandler()
formatter = logging.Formatter("%(asctime)s %(name)s %(levelname)s: %(message)s")
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)


class JavAPIHandler(BaseHTTPRequestHandler):
    """处理API请求的HTTP处理器"""

    def _send_json_response(self, data):
        """发送JSON格式的响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域请求
        self.end_headers()

    def _send_error(self, code, message):
        """发送错误响应"""
        self.send_response(code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps({
            "success": False,
            "error": message
        }, ensure_ascii=False).encode('utf-8'))

    def _handle_code_query(self, code):
        """处理基于番号的查询"""
        try:
            logger.info(f"处理番号查询: {code}")

            # 创建Movie对象
            movie = Movie(code)

            # 获取相关数据
            all_info = {}
            parallel_crawler(movie)  # 并行爬取多个网站的数据

            # 汇总数据
            info_summary(movie, all_info)

            # 生成相关文件名
            generate_names(movie)

            # 处理海报
            if movie.info.cover:
                process_poster(movie)

            # 构建返回数据
            result = {
                "code": code,
                "success": True,
                "data": movie.info.get_info_dic() if hasattr(movie, "info") else {},
            }

            # 如果有封面图片URL，添加到结果中
            if hasattr(movie.info, "cover") and movie.info.cover:
                result["data"]["cover_url"] = movie.info.cover

            return result

        except Exception as e:
            logger.error(f"处理番号 {code} 时出错: {e}", exc_info=True)
            return {"code": code, "success": False, "error": str(e)}

    def do_GET(self):
        """处理GET请求"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query_params = parse_qs(parsed_url.query)

        # API路由处理
        if path == "/api/movie":
            # 检查是否提供了code参数
            if "code" in query_params:
                code = query_params["code"][0]
                result = self._handle_code_query(code)
                self._send_response(result)
            else:
                self._send_response({"error": "缺少必要的参数: code"}, 400)

        elif path == "/api/health":
            # 健康检查接口
            self._send_response({"status": "ok"})

        else:
            # 处理不存在的API路径
            self._send_response({"error": "未找到请求的API路径"}, 404)

    def do_OPTIONS(self):
        """处理OPTIONS请求，支持CORS"""
        self._set_headers()

    def log_message(self, format, *args):
        """覆盖默认日志方法，使用我们自己的日志器"""
        logger.info(f"{self.address_string()} - {format % args}")


def run_server(host="0.0.0.0", port=8800):
    """运行API服务器"""
    server_address = (host, port)
    httpd = HTTPServer(server_address, JavAPIHandler)
    logger.info(f"启动API服务器: http://{host}:{port}/")
    httpd.serve_forever()


def entry():
    """服务器入口函数"""
    try:
        # 初始化配置
        Cfg()

        # 导入爬虫模块
        import_crawlers()

        # 解析命令行参数
        parser = argparse.ArgumentParser(description="JavSP API服务器")
        parser.add_argument(
            "--host", default="0.0.0.0", help="监听地址 (默认: 0.0.0.0)"
        )
        parser.add_argument(
            "--port", type=int, default=8800, help="监听端口 (默认: 8800)"
        )
        parser.add_argument("--debug", action="store_true", help="启用调试模式")

        args = parser.parse_args()

        # 设置日志级别
        if args.debug:
            logger.setLevel(logging.DEBUG)

        # 启动服务器
        run_server(args.host, args.port)

    except KeyboardInterrupt:
        logger.info("收到关闭信号，服务器正在关闭...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"服务器运行时出错: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    entry()
