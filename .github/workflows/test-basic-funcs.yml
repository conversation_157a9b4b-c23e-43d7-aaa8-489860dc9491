# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: "Unit Test: basic functions"

on:
  push:
    branches:
    - master
  pull_request:
    branches:
    - master
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  test-basic-funcs:

    name: Test basic funcs on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest]

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: 'true'

    - name: Install poetry
      run: pipx install poetry

    - name: Setup Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'poetry'
    - name: Setup dynamic versioning
      run: poetry self add poetry-dynamic-versioning
    - name: Install dependencies
      run: |
        poetry install
    - name: Test avid.py
      run: |
        poetry run pytest unittest/test_avid.py
    - name: Test file.py
      run: |
        poetry run pytest unittest/test_file.py
    - name: Test func.py
      run: |
        poetry run pytest unittest/test_func.py
    - name: Upload log as artifact
      uses: actions/upload-artifact@v4
      if: ${{ always() }}
      with:
        name: JavSP-basic-funcs-${{ matrix.os }}.log
        path: javspn/JavSP.log
