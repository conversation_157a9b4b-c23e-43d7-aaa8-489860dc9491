# vim:foldmethod=marker 
################################
scanner:
  # 推测番号前忽略文件名中的特定字符串（忽略大小写，以英文分号;分隔）
  # 大多数情况软件能够自动识别番号，只有当文件名中特定的部分导致番号识别错误时才需要更新此设置
  # 要忽略的正则表达式（如果你不熟悉正则表达式，请不要修改此配置，否则可能严重影响番号识别效果）
  ignored_id_pattern:
    - '(144|240|360|480|720|1080)[Pp]'
    - '[24][Kk]'
    - '\w+2048\.com'
    - 'Carib(beancom)?'
    - '[^a-z\d](f?hd|lt)[^a-z\d]'
  # 整理哪个文件夹下的影片？（此项留空时将在运行时询问）
  input_directory: null
  # 哪些后缀的文件应当视为影片？
  filename_extensions: [.3gp, .avi, .f4v, .flv, .iso, .m2ts, .m4v, .mkv, .mov, .mp4, .mpeg, .rm, .rmvb, .ts, .vob, .webm, .wmv, .strm, .mpg]
  # 扫描影片文件时忽略指定的文件夹
  ignored_folder_name_pattern: ['^\.', '^#recycle$', '^#整理完成$', '^#不要扫描$']
  # 匹配番号时忽略小于指定大小的文件
  # 格式要求：https://docs.pydantic.dev/2.0/usage/types/bytesize/
  minimum_size: 232MiB
  skip_nfo_dir: yes
  manual: yes
################################
network:
  # 设置代理服务器地址，支持 http, socks5/socks5h 代理，比如'http://127.0.0.1:1080'
  # null表示禁用代理
  proxy_server: null
  # 各个站点的免代理地址。地址失效时软件会自动尝试获取新地址，你也可以手动设置
  proxy_free:
    avsox: 'https://avsox.click'
    javbus: 'https://www.seedmm.help'
    javdb: 'https://javdb368.com'
    javlib: 'https://www.y78k.com'
  # 网络问题导致抓取数据失败时的重试次数，通常3次就差不多了
  retry: 3
  # https://en.wikipedia.org/wiki/ISO_8601#Durations
  timeout: PT10S

################################
crawler:
  # 要使用的爬虫列表（汇总数据时从前到后进行）
  # airav avsox avwiki fanza fc2 fc2fan javbus javdb javlib javmenu jav321 mgstage prestige arzon arzon_iv
  selection:
    normal: [airav, avsox, javbus, javdb, javlib, jav321, mgstage, prestige]
    fc2: [fc2, avsox, javdb, javmenu, fc2ppvdb]
    cid: [fanza]
    getchu: [dl_getchu]
    gyutto: [gyutto]
  # 爬虫至少要获取到哪些字段才可以视为抓取成功？
  required_keys: [cover, title]
  # 努力爬取更准确更丰富的信息（会略微增加部分站点的爬取耗时）
  hardworking: true
  # 使用网页番号作为最终番号（启用时会对番号大小写等进行更正）
  respect_site_avid: true
  # fc2fan已关站。如果你有镜像，请设置本地镜像文件夹的路径，此文件夹内要有类似'FC2-12345.html'的网页文件
  fc2fan_local_path: null
  # 刮削一部电影后的等待时间（设置为0禁用此功能）
  # https://en.wikipedia.org/wiki/ISO_8601#Durations
  sleep_after_scraping: PT1S
  # 是否使用javdb的封面（fallback/yes/no, 默认fallback: 如果能从别的站点获得封面则不用javdb的以避免水印）
  use_javdb_cover: fallback
  # 是否统一女优艺名。启用时会尝试将女优的多个艺名统一成一个
  normalize_actress_name: true

################################
# 配置整理时的命名规则
# path_pattern, nfo_title_pattern和name_pattern中可以使用变量来引用影片的数据，支持的变量列表见下面的地址:
# https://github.com/Yuukiy/JavSP/wiki/NamingRule-%7C-%E5%91%BD%E5%90%8D%E8%A7%84%E5%88%99
summarizer:
  # 整理时是否移动文件: true-移动所有文件到新文件夹; false-数据保存到同级文件夹，不移动文件
  move_files: true

  # 路径相关的选项
  path: 
    # 存放影片、封面等文件的文件夹路径
    output_folder_pattern: '#整理完成/{actress}/[{num}] {title}'
    # 影片、封面、nfo信息文件等的文件名将基于下面的规则来创建
    basename_pattern: '{num}'
    # 允许的最长文件路径（路径过长时将据此自动截短标题）
    length_maximum: 250
    # 是否以字节数来计算文件路径长度
    length_by_byte: true
    # 路径中的{actress}字段最多包含多少名女优？
    max_actress_count: 10
    # 是否用硬链接方式整理文件？硬链接可以节省空间，但不是所有文件系统都支持
    hard_link: false

  #标题处理
  title:
    # 删除尾部可能存在的女优名
    remove_trailing_actor_name: true

  # 下面这些项用来设置对应变量为空时的替代信息
  default:
    title: '#未知标题'
    actress: '#未知女优'
    series: '#未知系列'
    director: '#未知导演'
    producer: '#未知制作商'
    publisher: '#未知发行商'

  # NFO文件生成相关的选项
  nfo:
    # nfo文件的名称
    basename_pattern: "movie"
    # nfo文件中的影片标题（即媒体管理工具中显示的标题）
    title_pattern: '{num} {title}'
    # 要添加到自定义分类的字段，空列表表示不添加
    custom_genres_fields: ['{genre}', '{censor}']
    # 要添加到自定义标签的字段，空列表表示不添加
    custom_tags_fields: ['{genre}', '{censor}']
  # 依次设置 已知无码/已知有码/不确定 这三种情况下 {censor} 对应的文本(可以利用此变量将有码/无码影片整理到不同文件夹)
  censor_options_representation: ['无码', '有码', '打码情况未知']

  cover:
    # 封面文件的名称（不含拓展名），可以使用如`{title}`等字段
    basename_pattern: "poster"
    # 尽可能下载高清封面？（高清封面大小约 8-10 MiB，远大于普通封面，如果你的网络条件不佳，会降低整理速度）
    highres: true
    # 在封面图上添加水印（标签），例如“字幕”
    add_label: false
    crop:
      # 要使用图像识别来裁剪的番号系列需要匹配的正则表达式
      on_id_pattern:
        - '^\d{6}[-_]\d{3}$'
        - '^ARA'
        - '^SIRO'
        - '^GANA'
        - '^MIUM'
        - '^HHL'
      # 要使用的图像识别引擎，详细配置见文档 https://github.com/Yuukiy/JavSP/wiki/AI-%7C-%E4%BA%BA%E8%84%B8%E8%AF%86%E5%88%AB
      # NOTE: 此处无法直接对应，请参照注释手动填入
      engine: null # null表示禁用图像剪裁
      ## 使用Slimeface: {{{
      # engine: 
      #   name: slimeface
      ## }}}

  fanart:
    # 横版封面文件的名称（不含拓展名），可以使用如`{title}`等字段
    basename_pattern: "fanart"

  extra_fanarts:
    # 是否下载剧照？
    enabled: true
    # 间隔的两次封面爬取请求之间应该间隔多久
    scrap_interval: PT1.5S

################################
translator:
  # 翻译引擎，可选: google, bing, baidu, claude(haiku), openai （Google可以直接免费使用。留空表示禁用翻译功能）
  # 进阶功能的文档 https://github.com/Yuukiy/JavSP/wiki/Translation-%7C-%E7%BF%BB%E8%AF%91
  engine: null
  ## 使用百度翻译: {{{
  # engine: 
  #   name: baidu
  #   # 百度翻译的APP ID和密钥
  #   app_id: ''
  #   api_key: ''
  ## }}}
  ## 使用必应翻译: {{{
  # engine: 
  #   name: bing
  #   # 微软必应翻译（Azure 认知服务 → 翻译）的密钥
  #   api_key: ''
  ## }}}
  ## 使用Claude翻译: {{{
  # engine: 
  #   name: claude
  #   # Claude的密钥 (使用haiku模型)
  #   api_key: ''
  ## }}}
  ## 使用OpenAI翻译: {{{
  # engine: 
  #   name: openai
  #   # OpenAI API（默认使用 Groq，可替换成任何兼容 OpenAI 的第三方 API）
  #   url: 'https://api.groq.com/openai/v1/chat/completions'
  #   api_key: ''
  #   # 要使用的模型（默认使用 Groq 的 llama-3.1-70b-versatile 模型，若使用 OpenAI 官方 API 的话一般模型为 gpt-3.5-turbo）
  #   model: llama-3.1-70b-versatile
  ## }}}
  
  # 是否翻译各个字段
  fields: 
    # 是否翻译标题
    title: true
    # 是否翻译剧情简介
    plot: true
  
################################
other:
  # 是否在stdin/stdout进行交互
  interactive: true
  # 是否允许检查更新。如果允许，在有新版本时会显示提示信息和新版功能
  check_update: true
  # 是否允许检查到新版本时自动下载
  auto_update: false
